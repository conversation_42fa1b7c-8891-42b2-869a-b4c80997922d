import {
    TextField,
    FormControl,
    InputLabel,
    Checkbox,
    FormGroup,
    FormControlLabel,
    TextareaAutosize,
} from "@vapor/react-material";
import { Controller } from "react-hook-form";
import { SelectMultiple } from "./SelectMultiple";
import CustomError from "./CustomError";
import "./main.css";
import { CustomSelect } from "./CustomSelect";
export default function CustomTextField(props: any) {
    const propsObj = { ...props };

    delete propsObj.setValue;
    delete propsObj.control;

    const { options } = propsObj;
    return (
        <Controller
            name={props.name}
            control={props.control}
            render={({ field: { onChange, value }, fieldState: { error } }) => {
                if (props.type === "select") {
                    return (
                        <div
                            className={
                                props.name.includes("whereTypeSelection")
                                    ? "css-12l4ysv MuiTextField-root styledDiv"
                                    : "css-12l4ysv MuiTextField-root"
                            }
                        >
                            <FormControl {...propsObj}>
                                {props.label !== "" && (
                                    <InputLabel id={props.name + "-label"}>
                                        {props.label}
                                    </InputLabel>
                                )}
                                <CustomSelect
                                    {...propsObj}
                                    id={props.name}
                                    name={props.name}
                                    labelId={props.name + "-label"}
                                    hideLabel={props.label !== ""}
                                    onChange={({ target: { value } }: any) => {
                                        onChange(value);
                                        if (
                                            props.handleOnChange !==
                                                undefined &&
                                            props.customIndex !== undefined
                                        ) {
                                            props.handleOnChange(
                                                props.name,
                                                value,
                                                props.customIndex
                                            );
                                        } else if (
                                            props.handleOnChange !== undefined
                                        ) {
                                            props.handleOnChange(
                                                props.name,
                                                value
                                            );
                                        }
                                    }}
                                    value={
                                        props.formData !== undefined
                                            ? props.formData[props.name]
                                            : value !== undefined
                                            ? value
                                            : props.defaultValue !== undefined
                                            ? props.defaultValue
                                            : null
                                    }
                                />
                            </FormControl>
                            <CustomError error={error} />
                        </div>
                    );
                } else if (props.type === "checkbox") {
                    return (
                        <FormControl {...propsObj}>
                            <FormGroup>
                                {props.label !== "" && (
                                    <InputLabel htmlFor={props.name}>
                                        {props.label}
                                    </InputLabel>
                                )}
                                {(props.options || []).map((row: any) => {
                                    return (
                                        <FormControlLabel
                                            key={"checkbox" + row.label}
                                            value={row.value}
                                            control={
                                                <Checkbox
                                                    checked={
                                                        props.formData !==
                                                        undefined
                                                            ? props.formData[
                                                                  props.name
                                                              ] === row.value
                                                            : value ===
                                                              row.value
                                                            ? true
                                                            : props.defaultValue !==
                                                              undefined
                                                            ? props.defaultValue ===
                                                              row.value
                                                            : false
                                                    }
                                                />
                                            }
                                            name={props.name}
                                            label={row.label}
                                            labelPlacement="end"
                                            onChange={({ target }: any) => {
                                                onChange(
                                                    target.checked
                                                        ? target.value
                                                        : ""
                                                );
                                                if (
                                                    props.handleOnChange !==
                                                        undefined &&
                                                    props.customIndex !==
                                                        undefined
                                                ) {
                                                    props.handleOnChange(
                                                        props.name,
                                                        target.checked,
                                                        props.customIndex
                                                    );
                                                } else if (
                                                    props.handleOnChange !==
                                                    undefined
                                                ) {
                                                    props.handleOnChange(
                                                        props.name,
                                                        target.checked,
                                                        target.value
                                                    );
                                                }
                                            }}
                                        />
                                    );
                                })}
                            </FormGroup>
                            <CustomError error={error} />
                        </FormControl>
                    );
                } else if (props.type === "textarea") {
                    return (
                        <div className="css-12l4ysv MuiTextField-root">
                            <InputLabel htmlFor={props.name}>
                                {props.label}
                            </InputLabel>
                            <TextareaAutosize
                                {...propsObj}
                                aria-label={props.name}
                                minRows={3}
                                spellCheck="false"
                                name={props.name}
                                onChange={({ target: { value } }: any) => {
                                    onChange(value);
                                }}
                                value={value === null ? "" : value}
                            />
                            <CustomError error={error} />
                        </div>
                    );
                } else if (props.type === "multiselect") {
                    return (
                        <div className="css-12l4ysv MuiTextField-root">
                            <SelectMultiple
                                options={options ?? []}
                                name={props.name}
                                onChange={(name: string, value: any[]) => {
                                    onChange(value, name);
                                    if (
                                        props.handleOnChange !== undefined &&
                                        props.customIndex !== undefined
                                    ) {
                                        props.handleOnChange(
                                            props.name,
                                            value,
                                            props.customIndex
                                        );
                                    } else if (
                                        props.handleOnChange !== undefined
                                    ) {
                                        props.handleOnChange(props.name, value);
                                    }
                                }}
                                values={
                                    props.formData !== undefined
                                        ? props.formData[props.name]
                                        : null
                                }
                                label={props.label}
                                placeholder={props.placeholder}
                            />
                            <CustomError error={error} />
                        </div>
                    );
                } else if (props.type === "file") {
                    return (
                        <TextField
                            {...propsObj}
                            onChange={(event: any) => {
                                onChange(event.target.value);
                                props.handleFile(event);
                            }}
                            value={value === null ? "" : value}
                            error={!!error}
                            helperText={error?.message || ""}
                        />
                    );
                } else {
                    return (
                        <TextField
                            {...propsObj}
                            onChange={({ target: { value } }: any) => {
                                onChange(value);
                                if (
                                    props.handleOnChange !== undefined &&
                                    props.customIndex !== undefined
                                ) {
                                    props.handleOnChange(
                                        props.name,
                                        value,
                                        props.customIndex
                                    );
                                } else if (props.handleOnChange !== undefined) {
                                    props.handleOnChange(props.name, value);
                                }
                            }}
                            value={
                                props.formData !== undefined
                                    ? props.formData[props.name]
                                    : value !== undefined
                                    ? value
                                    : null
                            }
                            error={!!error}
                            helperText={error?.message || ""}
                        />
                    );
                }
            }}
        />
    );
}
