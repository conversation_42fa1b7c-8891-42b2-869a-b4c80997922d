import { Box, Button, Select, MenuItem } from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import ArrowLeftRoundedIcon from '@mui/icons-material/ArrowLeftRounded';
import ArrowRightRoundedIcon from '@mui/icons-material/ArrowRightRounded';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFilter } from "@fortawesome/pro-regular-svg-icons";
import {
    ICalendarButtonsProps,
} from "../typings/generalCalendar.interface";
import { gettingCalendarViewName, gettingCalendarView } from "../helpers/gettingCalendarViewName";
import { updateCalendar } from "../helpers/calendarHelper";

export default function CalendarNavigationButtons(props: ICalendarButtonsProps) {
    const {
        query,
        setQuery,
        calendarRef,
        monthTitle,
        setMonthTitle,
        t,
        children,
        leftPanelOpen,
        setLeftPanelOpen,
    } = props;

    const selectedView = gettingCalendarView(query.viewName || 'month');

    const handleNavigationClick = (action: any) => {
        try {
            const calendarAPI: any = calendarRef?.current?.getApi();

            if (!calendarAPI || !calendarAPI.currentData) {
                return;
            }

            const calendarViewName = gettingCalendarViewName(
                calendarAPI.currentData.currentViewType
            );

            action(calendarAPI);
            updateCalendar(
                calendarAPI,
                query,
                setQuery,
                setMonthTitle,
                t,
                calendarViewName
            );
        } catch (error) {
            console.error('Error in handleNavigationClick:', error);
        }
    };

   const changeView = (viewName: string) => {
        try {
            const calendarAPI = calendarRef?.current?.getApi();
            if (!calendarAPI) return;

            // Preserve the selected date when changing views
            let targetDate = null;
            if (query.date && query.date !== 0) {
                // Convert query.date to a proper Date object
                if (query.date > 1000000000000) {
                    targetDate = new Date(query.date);
                } else {
                    targetDate = new Date(query.date * 1000);
                }
            }

            // Change view with the preserved date
            if (targetDate && !isNaN(targetDate.getTime())) {
                calendarAPI.changeView(viewName, targetDate);
            } else {
                calendarAPI.changeView(viewName);
            }

            const calendarViewName = gettingCalendarViewName(viewName);
            const updatedQuery = { ...query, viewName: viewName };

            // Ensure the date is preserved in the query
            if (targetDate && !isNaN(targetDate.getTime())) {
                updatedQuery.date = Math.floor(targetDate.getTime() / 1000);
            }

            setQuery(updatedQuery);
            updateCalendar(
                calendarAPI,
                updatedQuery,
                setQuery,
                setMonthTitle,
                t,
                calendarViewName,
                true // Preserve selected date when changing views
            );
        } catch (error) {
            console.error('Error in changeView:', error);
        }
    };

    const getUperCaseTitle = (title: string): string => {
        const calendarAPI = calendarRef?.current?.getApi();
        if (!calendarAPI) return capitalizeFirstLetter(title);
        const currentView = (calendarAPI as any).currentData?.currentViewType;
        const currentDate = calendarAPI.getDate();

        if (currentView === "timeGridDay") {
            const day = currentDate.getDate();
            const month = currentDate.toLocaleString('it-IT', { month: 'long' });
            const year = currentDate.getFullYear();
            return `${day} ${capitalizeFirstLetter(month)} ${year}`;
        } else if (["timeGridWeek", "timeGridWorkWeek", "dayGridMonth"].includes(currentView)) {
            const formatOptions: Intl.DateTimeFormatOptions = {
                month: 'long',
                year: 'numeric'
            };
            const formattedDate = currentDate.toLocaleDateString('it-IT', formatOptions);
            return capitalizeFirstLetter(formattedDate);
        }

        return capitalizeFirstLetter(title);
    };

    const capitalizeFirstLetter = (string: string) =>
        string.charAt(0).toUpperCase() + string.slice(1);

    return (
        <Box sx={{ paddingBlock: 2, display: "flex", alignItems: "center" }}>
            {setLeftPanelOpen && (
                <Button
                    onClick={() => setLeftPanelOpen(!leftPanelOpen)}
                    variant={leftPanelOpen ? "contained" : "text"}
                    sx={{
                        minWidth: 'auto',
                        p: 1
                    }}
                >
                    <FontAwesomeIcon icon={faFilter} />
                </Button>
            )}

            {/* Left buttons */}
            <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                <Button
                    onClick={() =>
                        handleNavigationClick((api: any) => api.prev())
                    }
                >
                    <ArrowLeftRoundedIcon sx={{ width: 50, height: 50 }} />
                </Button>
                <Button
                    onClick={() =>
                        handleNavigationClick((api: any) => api.next())
                    }
                >
                    <ArrowRightRoundedIcon sx={{ width: 50, height: 50 }} />
                </Button>
            </Box>

            {/* Center title */}
            <Typography
                variant="titleMedium"
                component="div"
                color="primary.textTitleColor"
                sx={{ mx: 2 }}
            >
                {getUperCaseTitle(monthTitle as string)}
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', ml: 3 }}>
                <Button
                    variant="outlined"
                    onClick={() =>
                        handleNavigationClick((api: any) => api.today())
                    }
                >
                    {t("Oggi")}
                </Button>

                <Select
                    value={gettingCalendarView(selectedView || "month")}
                    onChange={(e: any) => changeView(e.target.value)}
                    variant="outlined"
                    sx={{ minWidth: 200 }}
                >
                    <MenuItem value="dayGridMonth">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {t("Mese")}
                        </Box>
                    </MenuItem>
                    <MenuItem value="timeGridWeek">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {t("Settimana")}
                        </Box>
                    </MenuItem>
                    <MenuItem value="timeGridWorkWeek">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {t("Settimana lavorativa")}
                        </Box>
                    </MenuItem>
                    <MenuItem value="timeGridDay">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {t("Giorno")}
                        </Box>
                    </MenuItem>
                </Select>
            </Box>

            <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                {children}
            </Box>
        </Box>
    );
}
