import { useState, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON>rid, <PERSON><PERSON>, <PERSON>u, MenuItem } from "@vapor/react-material";
import {
    DatePicker,
    LocalizationProvider,
    AdapterDateFns,
} from "@vapor/react-x-date-pickers";
import { Chip } from "@mui/material";
import { it } from "date-fns/locale";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faListCheck,
    faCalendar,
    faEllipsisVertical,
    faChevronDown,
} from "@fortawesome/pro-regular-svg-icons";
import { faCircleExclamation } from "@fortawesome/pro-solid-svg-icons";
import moment from "moment";
import { ICalendarButtonsProps } from "../typings/generalCalendar.interface";
import { updateCalendar } from "./calendarHelper";
import { gettingCalendarView } from "./gettingCalendarViewName";


export default function CalendarActionButtons(props: ICalendarButtonsProps) {
    const { query, setQuery, calendarRef, setMonthTitle, t } = props;
    const [openDatePicker, setOpenDatePicker] = useState(false);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const currentCalendarDate = useMemo(() => {
        if (query.date && query.date !== 0) {
            if (query.date > 1000000000000) {
                return moment(query.date).toDate();
            } else {
                return moment.unix(query.date).toDate();
            }
        }

        const calendarAPI = calendarRef?.current?.getApi();
        if (calendarAPI) {
            const date = calendarAPI.getDate();
            return date ? new Date(date) : new Date();
        }

        return new Date();
    }, [query.date, calendarRef]);

    const goToDate = (date: any) => {
        const calendarAPI = calendarRef?.current?.getApi();
        if (calendarAPI) {
            const isWeekend = moment(date).isoWeekday() >= 6;
            calendarAPI.setOption("weekends", isWeekend);

            const currentView = gettingCalendarView(query.viewName || 'month');
            calendarAPI.changeView(currentView, new Date(date));

            const selectedDate = moment(date);
            const startOfDay = selectedDate.clone().startOf('day');
            const endOfDay = selectedDate.clone().endOf('day');

            const updatedQuery = {
                ...query,
                start: startOfDay.unix(),
                end: endOfDay.unix(),
                date: selectedDate.unix(),
                calendarWeekends: isWeekend,
            };

            updateCalendar(
                calendarAPI,
                updatedQuery,
                setQuery,
                setMonthTitle,
                t,
                query.viewName || 'month',
                true // Preserve selected date when using datepicker
            );
        }
    };

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <Grid
            container
            spacing={2}
            justifyContent="flex-end"
            alignItems="center"
            sx={{ flexGrow: 1 }}
        >
            <Grid item md={1}>
                <Chip
                    icon={<FontAwesomeIcon icon={faCircleExclamation} style={{ fontSize: '1rem', color: 'white' }} />}
                    label="99+"
                    size="small"
                    sx={{
                        bgcolor: 'red',
                        color: 'white',
                        px: 0.5,
                        '& .MuiChip-icon': {
                            color: 'white'
                        },
                        '& .MuiChip-label': {
                            fontSize: '0.875rem',
                            fontWeight: 500
                        }
                    }}
                />
            </Grid>
            <Grid item md={2.5}>
                <Stack direction="row" spacing={0.3}>
                    <Button
                        variant="contained"
                        color="info"
                        sx={{
                            width: 80,
                            borderRadius: '8px 0 0 8px'
                        }}
                    >
                        {t("Crea")}
                    </Button>
                    <Button
                        variant="contained"
                        onClick={handleClick}
                        sx={{
                            width: 10,
                            borderRadius: '0 8px 8px 0'
                        }}
                    >
                        <FontAwesomeIcon icon={faChevronDown} />
                    </Button>
                    <Menu
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                        anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                        }}
                        transformOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                        }}
                    >
                        <MenuItem onClick={handleClose}>Opzione 1</MenuItem>
                        <MenuItem onClick={handleClose}>Opzione 2</MenuItem>
                        <MenuItem onClick={handleClose}>Opzione 3</MenuItem>
                    </Menu>
                </Stack>
            </Grid>
            <Grid item>
                <Button
                    onClick={() => setOpenDatePicker(true)}
                    disabled={false}
                >
                    <FontAwesomeIcon icon={faCalendar} size="lg" />
                </Button>
                <Button
                    disabled={false}
                >
                    <FontAwesomeIcon icon={faListCheck} size="lg" />
                </Button>
                <Button
                    disabled={false}
                >
                    <FontAwesomeIcon icon={faEllipsisVertical} size="lg" />
                </Button>
            </Grid>
            {openDatePicker && (
                <LocalizationProvider
                    dateAdapter={AdapterDateFns}
                    adapterLocale={it}
                >
                    <DatePicker
                        open={openDatePicker}
                        onClose={() => setOpenDatePicker(false)}
                        value={currentCalendarDate}
                        onChange={(date: any) => goToDate(date)}
                        format="dd/MM/yyyy"
                        slotProps={{
                            textField: {
                                error: false,
                                sx: { visibility: "hidden", width: 0 },
                            },
                            day: {
                                sx: {
                                    '&.MuiPickersDay-today': {
                                        backgroundColor: '#005075 !important',
                                        color: '#ffffff !important',
                                        fontWeight: 'bold !important',
                                        borderRadius: '50% !important',
                                        border: 'none !important',
                                    },
                                    '&.MuiPickersDay-today:hover': {
                                        backgroundColor: '#005075',
                                        color: '#ffffff',
                                    },
                                    '&.Mui-selected': {
                                        backgroundColor: '#005075 !important',
                                        color: '#ffffff !important',
                                        fontWeight: 'bold !important',
                                        borderRadius: '50% !important',
                                    },
                                    '&.Mui-selected:hover': {
                                        backgroundColor: '#005075',
                                        color: '#ffffff',
                                    },
                                }
                            }
                        }}
                    />
                </LocalizationProvider>
            )}
        </Grid>
    );
}
