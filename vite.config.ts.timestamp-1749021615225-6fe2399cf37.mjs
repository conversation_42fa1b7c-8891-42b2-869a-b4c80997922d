// vite.config.ts
import { oneFrontApp } from "file:///home/<USER>/netlex/full_day_engagement/netlex-1front/node_modules/@1f/vite-plugin/dist/index.js";
import react from "file:///home/<USER>/netlex/full_day_engagement/netlex-1front/node_modules/@vitejs/plugin-react-swc/index.mjs";
import { defineConfig } from "file:///home/<USER>/netlex/full_day_engagement/netlex-1front/node_modules/vite/dist/node/index.js";
var vite_config_default = defineConfig({
  plugins: [react(), oneFrontApp()]
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCIvaG9tZS91c2VyL25ldGxleC9mdWxsX2RheV9lbmdhZ2VtZW50L25ldGxleC0xZnJvbnRcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIi9ob21lL3VzZXIvbmV0bGV4L2Z1bGxfZGF5X2VuZ2FnZW1lbnQvbmV0bGV4LTFmcm9udC92aXRlLmNvbmZpZy50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vaG9tZS91c2VyL25ldGxleC9mdWxsX2RheV9lbmdhZ2VtZW50L25ldGxleC0xZnJvbnQvdml0ZS5jb25maWcudHNcIjtpbXBvcnQgeyBvbmVGcm9udEFwcCB9IGZyb20gXCJAMWYvdml0ZS1wbHVnaW5cIlxuaW1wb3J0IHJlYWN0IGZyb20gJ0B2aXRlanMvcGx1Z2luLXJlYWN0LXN3YydcbmltcG9ydCB7IGRlZmluZUNvbmZpZyB9IGZyb20gJ3ZpdGUnXG5cbmV4cG9ydCBkZWZhdWx0IGRlZmluZUNvbmZpZyh7XG4gIHBsdWdpbnM6IFtyZWFjdCgpLCBvbmVGcm9udEFwcCgpXSxcbn0pIl0sCiAgIm1hcHBpbmdzIjogIjtBQUEyVSxTQUFTLG1CQUFtQjtBQUN2VyxPQUFPLFdBQVc7QUFDbEIsU0FBUyxvQkFBb0I7QUFFN0IsSUFBTyxzQkFBUSxhQUFhO0FBQUEsRUFDMUIsU0FBUyxDQUFDLE1BQU0sR0FBRyxZQUFZLENBQUM7QUFDbEMsQ0FBQzsiLAogICJuYW1lcyI6IFtdCn0K
